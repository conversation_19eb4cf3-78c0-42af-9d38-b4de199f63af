import 'package:flutter_test/flutter_test.dart';
import 'package:guests/enums.dart';

void main() {
  group('BoxType initialization', () {
    test('should include all box types in values', () {
      // 验证 BoxType.values 包含所有预期的枚举值
      expect(BoxType.values.length, 4);
      expect(BoxType.values, contains(BoxType.order));
      expect(BoxType.values, contains(BoxType.orderDetail));
      expect(BoxType.values, contains(BoxType.orderInvoice));
      expect(BoxType.values, contains(BoxType.account));
    });

    test('should iterate through all box types', () {
      // 模拟 splash_controller 中的初始化逻辑
      final initializedBoxes = <BoxType>[];
      
      for (var boxType in BoxType.values) {
        initializedBoxes.add(boxType);
      }
      
      // 验证所有盒子类型都被处理
      expect(initializedBoxes.length, 4);
      expect(initializedBoxes, containsAll([
        BoxType.order,
        BoxType.orderDetail,
        BoxType.orderInvoice,
        BoxType.account,
      ]));
    });

    test('should maintain correct string values', () {
      // 验证枚举值对应的字符串值正确
      final expectedValues = {
        BoxType.order: 'order',
        BoxType.orderDetail: 'order_detail',
        BoxType.orderInvoice: 'order_invoice',
        BoxType.account: 'account',
      };

      for (var entry in expectedValues.entries) {
        expect(entry.key.value, entry.value);
      }
    });
  });
}
