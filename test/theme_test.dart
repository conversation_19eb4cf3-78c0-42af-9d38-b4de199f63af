import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:guests/app/themes/app_theme.dart';
import 'package:guests/app/themes/light_theme.dart';
import 'package:guests/app/themes/dark_theme.dart';
import 'package:guests/ok_colors.dart';

void main() {
  group('主题重构测试', () {
    test('AppTheme 应该提供正确的主题访问接口', () {
      // 测试当前主题
      expect(AppTheme.currentTheme, isA<ThemeData>());
      
      // 测试浅色主题
      expect(AppTheme.lightTheme, isA<ThemeData>());
      expect(AppTheme.lightTheme.brightness, Brightness.light);
      
      // 测试深色主题
      expect(AppTheme.darkTheme, isA<ThemeData>());
      expect(AppTheme.darkTheme.brightness, Brightness.dark);
    });

    test('LightTheme 应该返回正确配置的浅色主题', () {
      final theme = LightTheme.getThemeData();
      
      expect(theme.brightness, Brightness.light);
      expect(theme.primaryColor, OkColors.primary);
      expect(theme.colorScheme.primary, OkColors.primary);
      expect(theme.colorScheme.surface, OkColors.surface);
      expect(theme.scaffoldBackgroundColor, OkColors.primary);
    });

    test('DarkTheme 应该返回正确配置的深色主题', () {
      final theme = DarkTheme.getThemeData();
      
      expect(theme.brightness, Brightness.dark);
      expect(theme.primaryColor, OkColors.primary);
      expect(theme.colorScheme.primary, OkColors.primary);
      expect(theme.scaffoldBackgroundColor, const Color(0xFF121212));
    });

    test('AppTheme.getThemeData 应该根据模式返回正确的主题', () {
      // 测试浅色模式
      final lightTheme = AppTheme.getThemeData(ThemeMode.light);
      expect(lightTheme.brightness, Brightness.light);
      
      // 测试深色模式
      final darkTheme = AppTheme.getThemeData(ThemeMode.dark);
      expect(darkTheme.brightness, Brightness.dark);
      
      // 测试系统模式（默认返回浅色）
      final systemTheme = AppTheme.getThemeData(ThemeMode.system);
      expect(systemTheme.brightness, Brightness.light);
    });

    test('AppTheme.isDarkMode 应该正确判断主题模式', () {
      expect(AppTheme.isDarkMode(ThemeMode.light), false);
      expect(AppTheme.isDarkMode(ThemeMode.dark), true);
      expect(AppTheme.isDarkMode(ThemeMode.system), false);
    });

    test('AppTheme.getThemeModeName 应该返回正确的主题名称', () {
      expect(AppTheme.getThemeModeName(ThemeMode.light), '浅色主题');
      expect(AppTheme.getThemeModeName(ThemeMode.dark), '深色主题');
      expect(AppTheme.getThemeModeName(ThemeMode.system), '跟随系统');
    });

    test('主题应该包含所有必要的组件主题配置', () {
      final lightTheme = LightTheme.getThemeData();
      
      // 检查 AppBar 主题
      expect(lightTheme.appBarTheme.backgroundColor, OkColors.primary);
      expect(lightTheme.appBarTheme.elevation, 0.0);
      
      // 检查输入框主题
      expect(lightTheme.inputDecorationTheme.fillColor, Colors.white);
      expect(lightTheme.inputDecorationTheme.filled, true);
      
      // 检查复选框主题
      expect(lightTheme.checkboxTheme, isNotNull);
      
      // 检查标签栏主题
      expect(lightTheme.tabBarTheme.labelColor, Colors.white);
      expect(lightTheme.tabBarTheme.indicatorColor, OkColors.primary);
      
      // 检查列表项主题
      expect(lightTheme.listTileTheme.tileColor, OkColors.surface);
    });
  });
}
