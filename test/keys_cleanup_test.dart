import 'package:flutter_test/flutter_test.dart';
import 'package:guests/enums.dart';

void main() {
  group('Keys cleanup verification', () {
    test('BoxType enum should provide all necessary values', () {
      // 验证 BoxType 枚举包含所有必要的值
      expect(BoxType.values.length, 4);
      
      // 验证每个枚举值都有正确的字符串值
      expect(BoxType.order.value, 'order');
      expect(BoxType.orderDetail.value, 'order_detail');
      expect(BoxType.orderInvoice.value, 'order_invoice');
      expect(BoxType.account.value, 'account');
    });

    test('BoxType values should match expected storage box names', () {
      // 验证枚举值与预期的存储盒子名称匹配
      final expectedValues = {
        'order': BoxType.order,
        'order_detail': BoxType.orderDetail,
        'order_invoice': BoxType.orderInvoice,
        'account': BoxType.account,
      };

      for (var entry in expectedValues.entries) {
        expect(entry.value.value, entry.key);
      }
    });

    test('BoxType should be sufficient replacement for Keys constants', () {
      // 验证 BoxType 枚举可以完全替代之前的 Keys 常量
      
      // 模拟之前 Keys 常量的值
      const expectedBoxOrder = 'order';
      const expectedBoxOrderDetail = 'order_detail';
      const expectedBoxOrderInvoice = 'order_invoice';
      const expectedBoxAccount = 'account';

      // 验证 BoxType 枚举提供相同的值
      expect(BoxType.order.value, expectedBoxOrder);
      expect(BoxType.orderDetail.value, expectedBoxOrderDetail);
      expect(BoxType.orderInvoice.value, expectedBoxOrderInvoice);
      expect(BoxType.account.value, expectedBoxAccount);
    });
  });
}
