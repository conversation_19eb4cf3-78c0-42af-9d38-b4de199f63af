import 'package:flutter_test/flutter_test.dart';
import 'package:guests/enums.dart';

void main() {
  group('BoxType', () {
    test('should have correct enum values', () {
      expect(BoxType.values.length, 4);
      expect(BoxType.values, contains(BoxType.order));
      expect(BoxType.values, contains(BoxType.orderDetail));
      expect(BoxType.values, contains(BoxType.orderInvoice));
      expect(BoxType.values, contains(BoxType.account));
    });

    test('should return correct string values', () {
      expect(BoxType.order.value, 'order');
      expect(BoxType.orderDetail.value, 'order_detail');
      expect(BoxType.orderInvoice.value, 'order_invoice');
      expect(BoxType.account.value, 'account');
    });

    test('should maintain consistency with Keys constants', () {
      // 这些值应该与 Keys 类中的常量保持一致
      expect(BoxType.order.value, 'order');
      expect(BoxType.orderDetail.value, 'order_detail');
      expect(BoxType.orderInvoice.value, 'order_invoice');
      expect(BoxType.account.value, 'account');
    });
  });
}
