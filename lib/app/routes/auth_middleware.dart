import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/extension.dart';
import 'package:logger/logger.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final logger = Get.find<Logger>();
    if (Routes.LOGIN == route) {
      logger.d('[AuthMiddleware] redirect: route($route)');
      return super.redirect(route);
    }
    final prefProvider = Get.find<PrefProvider>();
    if (prefProvider.token.isEmpty) {
      logger.d('[AuthMiddleware] redirect: token is empty');
      return RouteSettings(name: Routes.LOGIN);
    }
    if (prefProvider.jwt.isExpired) {
      logger.d('[AuthMiddleware] redirect: token is expired');
      return RouteSettings(name: Routes.LOGIN);
    }
    return super.redirect(route);
  }
}
