import 'package:flutter/material.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/extension.dart';

import '../../ok_colors.dart';

class Avatar extends StatelessWidget {
  final StoreAccount data;
  final bool showStatus;

  const Avatar({
    Key? key,
    required this.data,
    this.showStatus = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(children: _items().toList());
  }

  Iterable<Widget> _items() sync* {
    yield _avatar();
    yield const SizedBox(width: 12.0);
    yield Expanded(child: _name());
    yield const SizedBox(width: 12.0);
    if (showStatus == true) {
      yield _status();
    }
  }

  Widget _avatar() {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: const [
          const BoxShadow(
            color: const Color(0x4dc68329),
            offset: const Offset(3.0, 3.0),
            blurRadius: 12.0,
          ),
        ],
      ),
      child: Icon(
        Icons.person_outline,
        size: 60.0,
        color: OkColors.outline,
      ),
    );
  }

  Widget _name() {
    Iterable<Widget> children() sync* {
      yield Text(
        data.role?.name ?? '',
        style: const TextStyle(
          fontSize: 15,
          color: const Color(0xff936230),
          height: 1.0,
        ),
        textAlign: TextAlign.left,
      );
      yield const SizedBox(height: 8.0);
      yield _subtitle();
    }

    return Align(
      alignment: Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _subtitle() {
    Iterable<Widget> children() sync* {
      yield Text(
        this.data.name ?? '',
        style: const TextStyle(
          fontSize: 22,
          color: Colors.black,
          fontWeight: FontWeight.w700,
          height: 1.0,
        ),
        textAlign: TextAlign.left,
      );
      yield const SizedBox(
        width: 8.0,
      );
      yield Text(
        data.comment ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: const Color(0xff5c4c4c),
          height: 1.0,
        ),
        textAlign: TextAlign.left,
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _status() {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 2.0,
      ),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(const Radius.circular(5.0)),
        color: data.displayStatusColor,
      ),
      child: Text(
        data.displayStatus,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
