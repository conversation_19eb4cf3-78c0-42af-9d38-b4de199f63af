import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class RoundedButton extends StatelessWidget {
  final String? text;
  final VoidCallback? onPressed;
  final Color color, textColor;
  final double width;
  final double height;

  const RoundedButton({
    Key? key,
    this.text,
    this.onPressed,
    this.color = const Color(0xFFF89321), // OkColors.primary
    this.textColor = Colors.white,
    this.height = kButtonHeight,
    this.width = double.infinity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(height * 0.5),
        gradient: LinearGradient(
          begin: const Alignment(-1.0, 0.0),
          end: const Alignment(1.0, 0.0),
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
          stops: const [0.0, 1.0],
        ),
      ),
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: const StadiumBorder(),
          minimumSize: Size(width, height),
        ),
        // style: ButtonStyle(
        //   padding: MaterialStateProperty.all(EdgeInsets.zero),
        //   shape: MaterialStateProperty.all(const StadiumBorder()),
        //   minimumSize: MaterialStateProperty.all(Size(width, height)),
        // ),
        child: Text(
          text ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
