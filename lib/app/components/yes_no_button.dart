import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class YesNoButton extends StatelessWidget {
  final String leftText;
  final String rightText;
  final VoidCallback? onLeftPressed;
  final VoidCallback? onRightPressed;

  const YesNoButton({
    Key? key,
    this.leftText = '取消',
    this.rightText = '確認',
    this.onLeftPressed,
    this.onRightPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Expanded(child: _leftButton(context)),
          Expanded(child: _rightButton(context)),
        ],
      ),
    );
  }

  Widget _rightButton(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          right: Radius.circular(25.0),
        ),
        gradient: kPrimaryGradient,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow,
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: TextButton(
        onPressed: onRightPressed,
        child: Text(
          rightText,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _leftButton(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(25.0),
        ),
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow,
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: TextButton(
        // style: TextButton.styleFrom(
        //   minimumSize: Size.fromHeight(40),
        //   padding: EdgeInsets.zero,
        //   backgroundColor: const Color(0xff3e4b5a),
        //   shape: RoundedRectangleBorder(
        //     borderRadius: BorderRadius.horizontal(
        //       left: Radius.circular(25.0),
        //     ),
        //   ),
        //   shadowColor: const Color(0x29000000),
        // ),
        onPressed: onLeftPressed,
        child: Text(
          leftText,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
