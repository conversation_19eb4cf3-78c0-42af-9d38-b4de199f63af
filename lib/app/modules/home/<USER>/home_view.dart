import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/modules/home/<USER>/home_controller.dart';
import 'package:guests/app/modules/settings/views/settings_view.dart';
import 'package:guests/app/modules/transactions/views/transactions_view.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/app/components/CircleButton.dart';
import 'package:guests/ok_colors.dart';

class HomeView extends GetView<HomeController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: controller.obx(
        (state) => _body(),
        onLoading: Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Icon(
            Icons.error,
            color: Colors.red,
          ),
        ),
      ),
      floatingActionButton: CircleButton(
        onPressed: () {
          final uri = Uri(
            path: Routes.CREATE_ORDER,
          );
          Get.toNamed(uri.toString());
        },
        icon: SvgPicture.asset('assets/images/icon_pos.svg'),
        text: Text(
          '結帳',
          style: TextStyle(
            fontSize: 15,
            color: OkColors.surfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: Obx(() => _bottomNavigationBar()),
    );
  }

  Widget _body() {
    return PageView.builder(
      controller: controller.pageController,
      itemCount: HomeMenu.values.length,
      onPageChanged: (index) {
        controller.homeMenu = HomeMenu.values[index];
      },
      itemBuilder: (context, index) {
        switch (HomeMenu.values[index]) {
          case HomeMenu.list:
            return TransactionsView();
          case HomeMenu.settings:
            return SettingsView();
        }
      },
    );
  }

  Widget _bottomNavigationBar() {
    return BottomNavigationBar(
      backgroundColor: OkColors.surfaceVariant,
      type: BottomNavigationBarType.fixed,
      currentIndex: _getBottomNavIndex(),
      onTap: (index) {
        // 跳過中間的空白佔位項目（索引1）
        if (index == 1) return;
        
        // 將底部導航欄索引映射到實際頁面索引
        int pageIndex = index > 1 ? index - 1 : index;
        controller.pageController.animateToPage(
          pageIndex,
          duration: 300.milliseconds,
          curve: Curves.ease,
        );
      },
      selectedItemColor: Get.theme.colorScheme.secondary,
      unselectedItemColor: OkColors.onSurface,
      selectedLabelStyle: TextStyle(
        fontSize: 12,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 12,
      ),
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.list),
          label: '消費紀錄',
        ),
        // 空白佔位項目，為中央浮動按鈕預留空間
        BottomNavigationBarItem(
          icon: SizedBox.shrink(), // 不顯示圖標
          label: '', // 不顯示標籤
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: '設定',
        ),
      ],
    );
  }

  /// 獲取底部導航欄的當前索引，考慮中間的空白佔位項目
  int _getBottomNavIndex() {
    int menuIndex = controller.homeMenu.index;
    // 如果是設定頁面（索引1），在底部導航欄中對應索引2（跳過中間的空白項目）
    return menuIndex == 1 ? 2 : menuIndex;
  }
}
