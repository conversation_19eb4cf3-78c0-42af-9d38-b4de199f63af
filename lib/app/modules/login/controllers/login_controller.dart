import 'dart:async';

import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/login_req.dart';
import 'package:guests/app/models/login_res.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/keys.dart';
import 'package:hive/hive.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';

class LoginController extends GetxController with StateMixin<String> {
  final _disposable = Completer();

  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Logger get logger => apiProvider.logger;
  PackageInfo get packageInfo => prefProvider.packageInfo;
  Box get userDefault => boxProvider.userDefault;

  final _rememberMe = false.obs;
  bool get rememberMe => _rememberMe.value;
  set rememberMe(bool value) => _rememberMe.value = value;

  final _draft = LoginReq().obs;
  LoginReq get draft => _draft.value;

  LoginController({
    required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _rememberMe.stream
        .debounce(500.milliseconds)
        .asyncMapSample((event) {
          logger.d('[LoginController] save rememberMe($event)');
          return userDefault.put(Keys.RememberMe, event);
        })
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      _rememberMe.value = userDefault.get(Keys.RememberMe, defaultValue: false);
    } catch (e) {
      logger.e('[LoginViewController] onRefresh: $e');
      _rememberMe.value = false;
    }
    if (rememberMe) {
      draft.clientCode = userDefault.get(Keys.ClientCode, defaultValue: '');
      draft.channelCode = userDefault.get(Keys.ChannelCode, defaultValue: '');
      draft.username = userDefault.get(Keys.Username, defaultValue: '');
    }
    change('', status: RxStatus.success());
  }

  Future<LoginRes> login() async {
    return await apiProvider.login(draft);
  }

  Future<void> onCheckBoxClicked() async {
    final checked = rememberMe;
    logger.d('[LoginViewController] onCheckBoxClicked: checked($checked)');
    rememberMe = !checked;
    if (rememberMe == false) {
      await _resetRememberMe();
    }
  }

  Future<void> _resetRememberMe() async {
    logger.d('[LoginViewController] _resetRememberMe');
    await userDefault.put(Keys.ClientCode, '');
    await userDefault.put(Keys.ChannelCode, '');
    await userDefault.put(Keys.Username, '');
  }

  Future<void> saveRememberMe() async {
    logger.d('[LoginViewController] _saveRememberMe');
    await userDefault.put(Keys.ClientCode, draft.clientCode);
    await userDefault.put(Keys.ChannelCode, draft.channelCode);
    await userDefault.put(Keys.Username, draft.username);
  }
}
