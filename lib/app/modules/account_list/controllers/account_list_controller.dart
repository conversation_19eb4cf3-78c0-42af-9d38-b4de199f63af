import 'dart:async';

import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/app/providers/account_provider.dart';
import 'package:guests/extension.dart';
import 'package:logger/logger.dart';

class AccountListController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final _fetching = Completer().obs;
  final _filter = Filter().obs;
  final _cached = <StoreAccount>[].obs;
  final AccountProvider accountProvider;
  Logger get logger => accountProvider.apiProvider.logger;
  Iterable<StoreAccount> get accounts => _cached;

  set keyword(String value) {
    _filter.value.keyword = value;
    refreshFilter();
  }

  AccountListController({
    required this.accountProvider,
  });

  @override
  void onInit() {
    super.onInit();
    accountProvider.storage
        .watch()
        .debounce(300.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      logger.d('[AccountListController] accountProvider.watch');
      final it = accountProvider.getStoreAccountsSync(_filter.value);
      final ls = List<StoreAccount>.from(it, growable: false);
      // 登入時間排序
      ls.sort((a, b) => b.displayLastLogin.compareTo(a.displayLastLogin));
      _cached.assignAll(ls);
      _updateStatus();
    });
    _filter.stream
        .tap((e) => change('', status: RxStatus.loading()))
        .debounce(700.milliseconds)
        .takeUntil(_disposable.future)
        .listen((value) => onRefresh());
  }

  @override
  void onReady() {
    super.onReady();
    refreshFilter();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    logger.d('[AccountListController] onRefresh');
    try {
      _fetching.value.complete();
      _fetching.value = Completer();
      accountProvider
          .getStoreAccounts(_filter.value)
          .takeUntil(_fetching.value.future)
          .listen((event) {});
      await 500.milliseconds.delay();
      _updateStatus();
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  void _updateStatus() {
    final status = accounts.isNotEmpty ? RxStatus.success() : RxStatus.empty();
    change('', status: status);
  }

  void refreshFilter() {
    _filter.refresh();
  }
}
