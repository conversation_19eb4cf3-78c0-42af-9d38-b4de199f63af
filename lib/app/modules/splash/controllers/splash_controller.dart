import 'package:get/get.dart';
import 'package:guests/app/providers/account_provider.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/order_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SplashController extends GetxController with StateMixin<String> {
  final BoxProvider boxProvider;
  final OrderProvider orderProvider;
  final AccountProvider accountProvider;

  Logger get logger => boxProvider.logger;
  ApiProvider get apiProvider => orderProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  PackageInfo get packageInfo => prefProvider.packageInfo;

  SplashController({
    required this.boxProvider,
    required this.orderProvider,
    required this.accountProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> onRefresh() async {
    // Set initial success state
    change('', status: RxStatus.success());
    if (prefProvider.isLogin) {
      await _initBox();
      await _fetchBrandsInfo();
      await _fetchChannelsInfo();
      await _fetchClientInfo();
      await _fetchUserInfo();
      await _fetchLatestOrders();
    }

    try {
      // Navigate to home and remove splash from navigation stack
      // This prevents users from navigating back to splash screen
      await Get.offAllNamed(Routes.HOME);
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
      // Set error state
      change(null, status: RxStatus.error('Failed to initialize application'));
    }
  }

  /// 下載品牌資訊
  Future<void> _fetchBrandsInfo() async {
    try {
      prefProvider.brandsInfo = await apiProvider.getBrandsInfo();
      logger.d('[SplashController] 取得 brandsInfo');
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
    }
  }

  /// 下載頻道資訊
  Future<void> _fetchChannelsInfo() async {
    try {
      prefProvider.channelsInfo = await apiProvider.getChannelsInfo();
      logger.d('[SplashController] 取得 channelsInfo');
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
    }
  }

  /// 下載客戶資訊
  Future<void> _fetchClientInfo() async {
    try {
      prefProvider.clientInfo = await apiProvider.getClientInfo();
      logger.d('[SplashController] 取得 clientInfo');
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
    }
  }

  /// 下載用戶資訊
  Future<void> _fetchUserInfo() async {
    try {
      final id = prefProvider.jwt.id;
      prefProvider.loginAccount = await accountProvider.getStoreAccount('$id');
      logger.d('[SplashController] 取得 loginAccount');
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
    }
  }

  /// 下載最新訂單
  Future<void> _fetchLatestOrders() async {
    try {
      await orderProvider.getOrders(page: 1, limit: kLimit);
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
    }
  }

  Future<void> _initBox() async {
    try {
      for (var boxType in BoxType.values) {
        await boxProvider.initGsBox(boxType);
        await boxProvider.initHiveLazyBox(boxType);
      }
    } catch (error, stackTrace) {
      logger.e(error.toString(), error: error, stackTrace: stackTrace);
    }
  }
}
