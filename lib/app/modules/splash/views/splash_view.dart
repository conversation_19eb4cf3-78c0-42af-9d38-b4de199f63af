import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/extension.dart';
import 'package:guests/ok_colors.dart';

import '../controllers/splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        backgroundColor: OkColors.primary,
        body: Background(
          background: SvgPicture.asset(
            'assets/images/background.svg',
            width: double.infinity,
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
          ),
          child: SafeArea(
            child: controller.obx(
              (state) => Center(
                child: _body(),
              ),
              onLoading: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              onError: (error) => const Center(
                child: Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 48,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // OMOS Logo
    yield SvgPicture.asset(
      'assets/images/omos_logo.svg',
      width: 68.dw,
      height: 68.dh,
    );
    yield SizedBox(height: 8.dh);
    yield Text(
      controller.packageInfo.appName,
      style: const TextStyle(
        fontSize: 19,
        color: Colors.white,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    );
    yield SizedBox(height: 16.dh);
    yield const CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
    );
  }

  Widget _body() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _children().toList(growable: false),
    );
  }
}
