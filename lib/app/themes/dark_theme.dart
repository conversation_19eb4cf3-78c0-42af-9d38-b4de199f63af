import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:guests/ok_colors.dart';

/// 深色主题配置
class DarkTheme {
  /// 获取深色主题数据
  static ThemeData getThemeData() {
    return ThemeData.dark().copyWith(
      brightness: Brightness.dark,
      primaryColor: OkColors.primary,
      colorScheme: const ColorScheme.dark(
        // 主要颜色
        primary: OkColors.primary,
        onPrimary: Colors.white,
        primaryContainer: Color(0xFF2D1B00),
        onPrimaryContainer: OkColors.primaryLight,

        // 次要颜色
        secondary: OkColors.secondary,
        onSecondary: Colors.white,
        secondaryContainer: Color(0xFF2D1B00),
        onSecondaryContainer: OkColors.primaryLight,

        // 表面颜色
        surface: Color(0xFF121212),
        onSurface: Color(0xFFE0E0E0),
        surfaceContainerHighest: Color(0xFF2C2C2C),
        onSurfaceVariant: Color(0xFFB0B0B0),
        surfaceContainer: Color(0xFF1E1E1E),

        // 错误颜色
        error: Color(0xFFFF6B6B),
        onError: Colors.white,
        errorContainer: Color(0xFF93000A),
        onErrorContainer: Color(0xFFFFDAD6),

        // 边框和分割线
        outline: Color(0xFF666666),
        outlineVariant: Color(0xFF404040),

        // 其他
        surfaceTint: Colors.transparent,
        shadow: Color(0x80000000),
        scrim: Color(0x80000000),
        inverseSurface: Color(0xFFE6E1E5),
        onInverseSurface: Color(0xFF313033),
        inversePrimary: OkColors.primary,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: OkColors.primary,
        elevation: 0.0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        iconTheme: IconThemeData(
          color: Colors.white,
        ),
        titleTextStyle: TextStyle(
          fontSize: 20,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      scaffoldBackgroundColor: Color(0xFF121212),
    );
  }
}
