import 'package:flutter/material.dart';
import 'package:guests/app/themes/light_theme.dart';
import 'package:guests/app/themes/dark_theme.dart';

/// 应用程序主题管理器
/// 
/// 提供统一的主题访问接口，支持浅色和深色主题切换
class AppTheme {
  /// 获取当前使用的主题（默认为浅色主题）
  static ThemeData get currentTheme => lightTheme;

  /// 获取浅色主题
  static ThemeData get lightTheme => LightTheme.getThemeData();

  /// 获取深色主题
  static ThemeData get darkTheme => DarkTheme.getThemeData();

  /// 主题模式枚举
  static const ThemeMode defaultThemeMode = ThemeMode.light;

  /// 根据主题模式获取对应的主题数据
  /// 
  /// [themeMode] 主题模式
  /// 返回对应的 ThemeData
  static ThemeData getThemeData(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return lightTheme;
      case ThemeMode.dark:
        return darkTheme;
      case ThemeMode.system:
        // 可以根据系统设置返回对应主题，这里默认返回浅色主题
        return lightTheme;
    }
  }

  /// 判断是否为深色主题
  /// 
  /// [themeMode] 主题模式
  /// 返回是否为深色主题
  static bool isDarkMode(ThemeMode themeMode) {
    return themeMode == ThemeMode.dark;
  }

  /// 获取主题模式的显示名称
  /// 
  /// [themeMode] 主题模式
  /// 返回主题模式的中文显示名称
  static String getThemeModeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return '浅色主题';
      case ThemeMode.dark:
        return '深色主题';
      case ThemeMode.system:
        return '跟随系统';
    }
  }
}
