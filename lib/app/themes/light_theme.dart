import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:guests/ok_colors.dart';
import 'package:guests/constants.dart';

/// 浅色主题配置
class LightTheme {
  /// 获取浅色主题数据
  static ThemeData getThemeData() {
    return ThemeData.light().copyWith(
      brightness: Brightness.light,
      primaryColor: OkColors.primary,
      colorScheme: const ColorScheme.light(
        // 主要颜色
        primary: OkColors.primary,
        onPrimary: Colors.white,
        primaryContainer: OkColors.primaryLight,
        onPrimaryContainer: OkColors.primary,

        // 次要颜色
        secondary: OkColors.secondary,
        onSecondary: Colors.white,
        secondaryContainer: OkColors.primaryLight,
        onSecondaryContainer: OkColors.secondary,

        // 表面颜色
        surface: OkColors.surface,
        onSurface: OkColors.onSurface,
        surfaceContainerHighest: OkColors.surfaceVariant,
        onSurfaceVariant: OkColors.onSurfaceVariant,
        surfaceContainer: OkColors.surfaceContainer,

        // 错误颜色
        error: OkColors.error,
        onError: Colors.white,
        errorContainer: Color(0xFFFFEDEA),
        onErrorContainer: OkColors.error,

        // 边框和分割线
        outline: OkColors.outline,
        outlineVariant: Color(0xFFE0E0E0),

        // 其他
        surfaceTint: Colors.transparent,
        shadow: Color(0x29000000),
        scrim: Color(0x80000000),
        inverseSurface: Color(0xFF2D3135),
        onInverseSurface: Color(0xFFF1F0F4),
        inversePrimary: Color(0xFFFFB77C),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: OkColors.primary,
        elevation: 0.0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        iconTheme: IconThemeData(
          color: Colors.white,
        ),
        titleTextStyle: TextStyle(
          fontSize: 20,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      actionIconTheme: ActionIconThemeData(
        backButtonIconBuilder: (context) => const SizedBox(
          width: 40,
          height: 40,
          child: DecoratedBox(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Color(0x80ffffff),
            ),
            child: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
          ),
        ),
      ),
      scaffoldBackgroundColor: OkColors.primary,
      switchTheme: SwitchThemeData(
        thumbColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.withOpacity(0.5);
          }
          if (states.contains(WidgetState.selected)) {
            return OkColors.primary;
          }
          return Colors.grey;
        }),
        trackColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.withOpacity(0.3);
          }
          if (states.contains(WidgetState.selected)) {
            return OkColors.primary.withOpacity(0.5);
          }
          return Colors.grey.withOpacity(0.3);
        }),
        overlayColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return OkColors.primary.withOpacity(0.1);
          }
          if (states.contains(WidgetState.hovered)) {
            return Colors.grey.withOpacity(0.1);
          }
          return null;
        }),
      ),
      indicatorColor: OkColors.primary,
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: OkColors.primary,
      ),
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: OkColors.primary,
        selectionColor: OkColors.primary,
        selectionHandleColor: OkColors.primary,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        fillColor: Colors.white,
        filled: true,
        contentPadding: EdgeInsets.symmetric(
          vertical: 8.0,
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: OkColors.outline),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: OkColors.primary),
        ),
        hintStyle: TextStyle(
          fontSize: 16.0,
          color: OkColors.onSurfaceVariant,
        ),
        labelStyle: TextStyle(
          fontSize: 16.0,
          color: OkColors.onSurface,
        ),
        prefixStyle: TextStyle(
          fontSize: 16.0,
          color: OkColors.onSurfaceVariant,
        ),
        suffixStyle: TextStyle(
          fontSize: 16.0,
          color: OkColors.onSurfaceVariant,
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return OkColors.outline;
          }
          if (states.contains(WidgetState.selected)) {
            return OkColors.primary;
          }
          return null;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
        overlayColor: WidgetStateProperty.all(OkColors.primary.withOpacity(0.1)),
      ),
      tabBarTheme: const TabBarTheme(
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: BoxDecoration(
          color: OkColors.primary,
        ),
        labelColor: Colors.white,
        indicatorColor: OkColors.primary,
        labelPadding: EdgeInsets.zero,
        labelStyle: TextStyle(
          fontSize: 16,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 16,
        ),
      ),
      listTileTheme: const ListTileThemeData(
        tileColor: OkColors.surface,
        contentPadding: kContentPadding,
        leadingAndTrailingTextStyle: TextStyle(
          fontSize: 16,
          color: OkColors.onSurface,
        ),
        titleTextStyle: TextStyle(
          fontSize: 16,
          color: OkColors.onSurface,
        ),
      ),
    );
  }
}
