import 'dart:ui';

/// 统一的颜色定义类
/// 使用语义化命名，支持主题系统
class OkColors {
  // === 主要品牌颜色 ===
  /// 主色调 - 橙色
  static const primary = Color(0xFFF89321);

  /// 次要色调 - 红橙色
  static const secondary = Color(0xFFE66F53);

  /// 主色调浅色版本
  static const primaryLight = Color(0xFFF1E6FF);

  /// 错误颜色
  static const error = Color(0xFFE00707);

  // === 背景颜色 ===
  /// 主背景色
  static const background = Color(0xFFEEEEF3);

  /// 表面颜色（卡片、对话框等）
  static const surface = Color(0xFFFFFFFF);

  // === 文本颜色 ===
  /// 主要文本颜色（标题）
  static const onSurface = Color(0xFF333333);

  /// 次要文本颜色（内容）
  static const onSurfaceVariant = Color(0xFF666666);

  // === 中性色系 ===
  /// 浅灰色 - 用于边框、分割线 (调整为符合 3:1 对比度标准)
  static const outline = Color(0xFF888888);

  /// 极浅灰色 - 用于背景
  static const surfaceVariant = Color(0xFFF7F7F7);

  /// 浅灰色背景
  static const surfaceContainer = Color(0xFFF0F0F0);

}
