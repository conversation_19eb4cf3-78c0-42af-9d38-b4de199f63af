name: guests
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.2.0+47

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_svg_provider: ^1.0.4
  get_storage: ^2.1.1
  jwt_decoder: ^2.0.1
  path_provider: ^2.1.1
  path: ^1.8.2
  package_info_plus: ^4.2.0  # Replaced discontinued package_info
  dio: ^5.0.0
  intl: ^0.19.0
  flutter_svg: ^2.0.5
  logger: ^2.0.2
  barcode: ^2.2.9
  get: ^4.6.6
  screenshot: ^3.0.0
  encrypt: ^5.0.1
  stream_transform: ^2.1.0
  xml: ^6.2.2
  sizer: ^3.0.0
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_sunmi_printer:
    git:
      url: https://bitbucket.org/umomos/flutter_sunmi_printer.git
      ref: a6d06bd3b5fbde22569ccb93fc8143372ef395e7

dependency_overrides:
  win32: 5.5.4

dev_dependencies:
  build_runner: ^2.3.3
  flutter_test:
    sdk: flutter
  mockito: ^5.4.0
  http: ^0.13.6

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/models/
    - assets/images/
    - assets/icons/
    - assets/data/
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

# https://medium.com/@psyanite/how-to-add-app-launcher-icons-in-flutter-bd92b0e0873a
# https://ab20803.medium.com/flutter-%E5%85%A9%E5%80%8B%E5%B9%B3%E5%8F%B0app-icon%E7%9A%84%E8%A8%AD%E7%BD%AE%E6%96%B9%E5%BC%8F-647e7bc2e680
# flutter packages pub run flutter_launcher_icons:main
flutter_icons:
  ios: true
  android: true
  image_path_ios: "assets/icons/icon.png"
  image_path_android: "assets/icons/ic_launcher.png"
  # adaptive_icon_background: "assets/icons/background.png"
  # adaptive_icon_foreground: "assets/icons/foreground.png"
